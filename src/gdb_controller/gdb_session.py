"""
GDB会话管理
"""

import asyncio
import subprocess
import signal
import os
import sys
import threading
import re
import time
import select
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from loguru import logger
import queue

from ..utils.debug_logger import DebugLogger, print_console


@dataclass
class StackFrame:
    """栈帧信息"""
    level: int
    function: str
    file: str
    line: int
    address: str
    args: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.args is None:
            self.args = {}


@dataclass
class Variable:
    """变量信息"""
    name: str
    type: str
    value: str
    address: Optional[str] = None
    size: Optional[int] = None


@dataclass
class BreakpointInfo:
    """断点信息"""
    number: int
    type: str
    disposition: str
    enabled: bool
    address: str
    location: str
    hit_count: int = 0

@dataclass
class DebugState:
    """调试状态"""
    is_running: bool
    is_stopped: bool
    stop_reason: str
    current_frame: Optional[StackFrame]
    stack_frames: List[StackFrame]
    variables: Dict[str, Variable]
    breakpoints: List[BreakpointInfo]
    signal_info: Optional[Dict[str, Any]] = None


class GDBSession:
    """GDB会话管理器"""
    
    def __init__(self, config):
        self.config = config
        self.process: Optional[subprocess.Popen] = None
        self.is_connected = False
        self.debug_state = DebugState(
            is_running=False,
            is_stopped=False,
            stop_reason="",
            current_frame=None,
            stack_frames=[],
            variables={},
            breakpoints=[]
        )
        self._command_timeout = config.gdb_command_timeout
        self._output_buffer = ""
        self.debug_logger = DebugLogger(config)
        self._session_start_time = None
        self.output_queue = queue.Queue()
    
    async def start_session(self, executable_path: Optional[str] = None) -> bool:
        """启动GDB会话"""
        try:
            # 记录会话开始时间
            self._session_start_time = time.time()

            # 构建GDB命令
            gdb_cmd = [str(self.config.gdb_path), "--interpreter=mi", "--quiet"]

            target_path = executable_path or str(self.config.target_executable)
            if target_path:
                gdb_cmd.append(target_path)

            # 记录GDB会话启动
            args = self.config.target_args.split() if self.config.target_args else []
            self.debug_logger.log_gdb_session_start(target_path, args)

            logger.info(f"启动GDB会话: {' '.join(gdb_cmd)}")

            # 启动GDB进程
            self.process = subprocess.Popen(
                gdb_cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            self._start_reader()
           
            # 等待GDB启动
            start_time = time.time()
            while time.time() - start_time < 5:
                if self.process.poll() is None:
                    break
                await asyncio.sleep(0.1)  # 短暂等待，避免高CPU占用

            # 检查进程是否正常启动
            if self.process.poll() is not None:
                logger.error("GDB进程启动失败")
                return False

            self.is_connected = True
            logger.info("GDB会话启动成功")

            # 等待GDB完全准备就绪
            await self._wait_for_gdb_ready()

            # 设置基本配置
            await self._setup_gdb()

            return True
            
        except BaseException as e:
            logger.error(f"启动GDB会话失败: {e}")
            return False
    def _start_reader(self):
        def reader():
            for line in self.process.stdout:
                self.output_queue.put(line)
        t = threading.Thread(target=reader, daemon=True)
        t.start()
    
    def _send_command(self, command):
        if self.process:
            self.process.stdin.write(command + '\n')
            self.process.stdin.flush()
    def _get_output(self, timeout=2) -> str:
        lines = []
        start = time.time()
        while True:
            try:
                line = self.output_queue.get(timeout=timeout)
                lines.append(line)
                # 增加对 GDB 提示符的检测
                if line.strip() == "(gdb)":
                    break
                if time.time() - start > timeout:
                    break
            except queue.Empty:
                break
        return ''.join(lines)
    def _clear_output_queue(self):
        """清空输出队列中的所有消息"""
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except queue.Empty:
                break

    def close(self):
        if self.process:
            self.send_command('quit')
            self.process.terminate()    
    async def _wait_for_gdb_ready(self):
        """等待GDB完全准备就绪"""
        try:
            logger.debug("等待GDB准备就绪...")
            start_time = time.time()
            ready_timeout = 10  # 最多等待10秒

            # 读取初始输出直到看到(gdb)提示符
            while time.time() - start_time < ready_timeout:
                if self.process.poll() is not None:
                    logger.error("GDB进程在初始化期间退出")
                    return False
                
                lines = self._get_output()
                for line in lines.splitlines():
                    if line and line.strip() == "(gdb)":
                        # 检查是否看到GDB提示符，表示准备就绪
                        logger.debug("GDB准备就绪")
                        return True
                    # 如果看到错误信息，记录但继续等待
                    if line.startswith("^error"):
                        logger.warning(f"GDB初始化时出现错误: {line}")                
                # 短暂休眠
                await asyncio.sleep(0.1)

            # 超时但没有看到提示符，尝试发送一个简单命令测试
            logger.warning("未在预期时间内看到GDB提示符，尝试测试连接...")
            try:
                # 发送一个简单的命令测试GDB是否响应
                out_lines = self.execute_command("show version")
                for line in out_lines.splitlines():
                    if line and line.startswith("^done"):
                        logger.info("GDB连接测试成功")
                        return True

                logger.warning("GDB连接测试超时")
                return False

            except Exception as e:
                logger.error(f"GDB连接测试失败: {e}")
                return False

        except BaseException as e:
            logger.error(f"等待GDB准备就绪失败: {e}")
            return False

    async def _setup_gdb(self):
        """设置GDB基本配置"""
        try:
            # 设置分页关闭
            await self.execute_command("set pagination off")
            
            # 设置确认关闭
            await self.execute_command("set confirm off")
            
            # 设置打印设置
            await self.execute_command("set print pretty on")
            await self.execute_command("set print array on")
            
            # 如果启用信号捕获
            if self.config.enable_signal_capture:
                await self.execute_command("handle SIGSEGV stop print")
                await self.execute_command("handle SIGABRT stop print")
                await self.execute_command("handle SIGFPE stop print")

                # 等待信号处理命令完全完成，清理输出缓冲
                await asyncio.sleep(1.0)

                # 执行一个简单命令来清理缓冲区
                await self.execute_command("show version")

            logger.info("GDB基本配置完成")
            
        except BaseException as e:
            logger.error(f"GDB配置失败: {e}")
    
    async def execute_command(self, command: str, command_timeout:int = 1) -> str:
        """执行GDB命令"""        
        if not self.is_connected or not self.process:
            raise RuntimeError("GDB会话未连接")
        
        try:
            logger.info(f"执行GDB命令: {command}")
            # 清理之前命令的输出
            self._clear_output_queue()
            self._send_command(command)
            
            try:
                await asyncio.sleep(0.1)
            except BaseException:
                pass
            
            output = self._get_output(command_timeout)
            logger.info(f"GDB-OUT: {output}")
            return output
        except BaseException as e:
            logger.error(f"执行GDB命令失败: {e}")
            return ""

        # try:
        #     await self.clear_console(2)
        #     start_time = time.time()
        #     logger.info(f"执行GDB命令: {command}")

        #     # 根据命令类型调整超时时间
        #     original_timeout = self._command_timeout
        #     self._command_timeout = original_timeout #self._get_command_timeout(command)

        #     try:
        #         # 发送命令
        #         self.process.stdin.write(f"{command}\n")
        #         self.process.stdin.flush()

        #         # 读取输出
        #         output = await self._read_output(command)

        #         # 记录命令执行时间和输出
        #         execution_time = time.time() - start_time
        #         self.debug_logger.log_gdb_command(command, output, execution_time)

        #         logger.debug(f"GDB命令'{command}'执行完成 ({execution_time:.2f}s): {output[:100]}...")
        #         return output

        #     finally:
        #         # 恢复原始超时时间
        #         self._command_timeout = original_timeout

        # except Exception as e:
        #     logger.error(f"执行GDB命令'{command}'失败: {type(e).__name__}: {e}")
        #     raise
    

    # async def _read_output(self, command: str) -> str:
    #     """读取GDB命令输出，带健壮的异常处理和超时控制"""
    #     output_lines = []
    #     try:
    #         start_time = time.time()
    #         command_completed = False
    #         consecutive_empty_reads = 0
    #         max_empty_reads = 20  # 增加最大空读取次数

    #         while True:
    #             # 超时检查
    #             elapsed_time = time.time() - start_time
    #             if elapsed_time > self._command_timeout:
    #                 logger.warning(f"GDB命令'{command}'执行超时 ({elapsed_time:.2f}s)")
    #                 break

    #             # 进程状态检查
    #             if self.process.poll() is not None:
    #                 logger.warning("GDB进程已退出")
    #                 break

    #             # 读取一行
    #             try:
    #                 line = await self._read_line()
    #                 if line:
    #                     output_lines.append(line)
    #                     consecutive_empty_reads = 0

    #                     # 检查MI模式的命令完成标志
    #                     if self._is_command_complete(line, command):
    #                         command_completed = True
    #                         logger.debug(f"检测到命令完成标志: {line}")
    #                         break

    #                 else:
    #                     consecutive_empty_reads += 1
    #                     # 如果连续多次读取为空，且已有输出，可能命令已完成
    #                     if consecutive_empty_reads >= max_empty_reads and output_lines:
    #                         logger.debug(f"连续{consecutive_empty_reads}次空读取，可能命令已完成")
    #                         break

    #             except asyncio.CancelledError:
    #                 logger.debug(f"GDB命令'{command}'读取被取消")
    #                 break
    #             except Exception as e:
    #                 logger.debug(f"读取行时发生异常: {type(e).__name__}: {e}")
    #                 consecutive_empty_reads += 1
    #                 # 如果异常过多，退出循环
    #                 if consecutive_empty_reads >= max_empty_reads:
    #                     logger.warning(f"读取异常过多，退出命令'{command}'的输出读取")
    #                     break
    #                 continue

    #             # 短暂休眠，避免CPU占用过高
    #             await asyncio.sleep(0.02)  # 增加休眠时间

    #         if command_completed:
    #             logger.debug(f"GDB命令'{command}'执行完成，输出{len(output_lines)}行")
    #         else:
    #             logger.debug(f"GDB命令'{command}'可能未正常完成，已收集{len(output_lines)}行输出")

    #         return "\n".join(output_lines)

    #     except asyncio.CancelledError:
    #         logger.debug(f"GDB命令'{command}'输出读取被取消")
    #         return "\n".join(output_lines)  # 返回已收集的输出
    #     except Exception as e:
    #         logger.error(f"读取GDB命令'{command}'输出失败: {type(e).__name__}: {e}")
    #         return "\n".join(output_lines)  # 返回已收集的输出
    # 读取控制台已有的输出
    async def clear_console(self, timeout: int = 5) -> str:
        try:
            start_time = time.time()
            consecutive_empty_reads = 0

            while True:
                # 超时检查
                if time.time() - start_time > timeout:
                    break

                # 进程状态检查
                if self.process.poll() is not None:
                    logger.error("GDB进程已退出")
                    break

                # 读取一行
                try:
                    line = await self._read_line()
                    if line:
                        logger.info(f"GDB输出: {line}")
                        consecutive_empty_reads = 0
                    else:
                        consecutive_empty_reads += 1
                        # 如果连续多次读取为空，且已有输出，可能命令已完成
                        if consecutive_empty_reads >= 10 :
                            logger.debug(f"连续{consecutive_empty_reads}次空读取，可能命令已完成")
                            break

                except BaseException as e:
                    logger.error(f"读取行失败: {e}")
                    consecutive_empty_reads += 1
                    continue

                # 短暂休眠，避免CPU占用过高
                await asyncio.sleep(0.01)

            return ""

        except BaseException as e:
            logger.error(f"读取GDB输出失败: {e}")
            return ""
    def _is_command_complete(self, line: str, command: str) -> bool:
        """检查命令是否完成，支持更多的完成标志"""
        line_stripped = line.strip()
        command_lower = command.strip().lower()

        # MI模式的命令完成标志
        if line_stripped.startswith(("^done", "^error", "^exit")):
            return True

        # 对于某些特殊命令，^running也表示完成
        if line_stripped.startswith("^running") and command_lower in ["run", "continue", "c"]:
            return True

        # 对于quit命令，检查特殊情况
        if command_lower in ["quit", "q", "exit"] and line_stripped.startswith("^"):
            return True

        # 检查GDB提示符（非MI模式）
        if line_stripped == "(gdb)":
            return True

        # 检查程序退出标志
        if line_stripped.startswith("*stopped") and "exited" in line_stripped:
            return True

        # 检查断点命中标志
        if line_stripped.startswith("*stopped") and "breakpoint-hit" in line_stripped:
            return True

        return False
    
    async def _read_line(self) -> str:
        """读取一行输出，带超时控制"""
        if not self.process or not self.process.stdout:
            return ""

        try:
            # 使用asyncio.wait_for添加超时控制
            loop = asyncio.get_event_loop()

            # 设置单行读取超时为1秒，避免无限期阻塞
            line = await asyncio.wait_for(
                loop.run_in_executor(None, self._readline_with_timeout),
                timeout=1.0
            )
            line = line.strip() if line else ""
            if line:
                logger.info(f"GDB-OUT: {line}")
            return line

        except BaseException as e:
            logger.error(f"读取行时发生异常: {e}")
            return ""

    def _readline_with_timeout(self) -> str:
        """带超时的readline实现"""
        if not self.process or not self.process.stdout:
            return ""

        try:
            # 检查是否有数据可读
            import select

            # 在Unix系统上使用select检查是否有数据可读
            if hasattr(select, 'select'):
                ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                if ready:
                    return self.process.stdout.readline()
                else:
                    return ""
            else:
                # Windows系统回退到直接读取
                return self.process.stdout.readline()

        except BaseException as e:
            logger.error(f"readline_with_timeout异常: {e}")
            return ""
    
    async def run_program(self, args: List[str] = None) -> bool:
        """运行程序"""
        try:
            # 构建运行命令
            if args:
                run_cmd = f"run {' '.join(args)}"
            else:
                run_cmd = "run"

            logger.info(f"运行程序: {run_cmd}")

            # 执行运行命令
            await self.execute_command(run_cmd, 5)

            return True

        except Exception as e:
            logger.error(f"运行程序失败: {e}")
            return False
    
    async def attach_process(self, pid: int) -> bool:
        """附加到进程"""
        try:
            logger.info(f"附加到进程: {pid}")
            
            # 执行附加命令
            output = await self.execute_command(f"attach {pid}")
            
            # 检查是否成功
            if "Attaching to" in output:
                logger.info("成功附加到进程")
                #await self._update_debug_state()
                return True
            else:
                logger.error(f"附加进程失败: {output}")
                return False
                
        except Exception as e:
            logger.error(f"附加进程失败: {e}")
            return False
    
    async def continue_execution(self) -> bool:
        """继续执行"""
        try:
            logger.info("继续执行程序")

            await self.execute_command("continue")
            #await self._update_debug_state()

            return True

        except Exception as e:
            logger.error(f"继续执行失败: {e}")
            return False
    
    async def step_over(self) -> bool:
        """单步执行（跳过函数调用）"""
        try:
            logger.info("单步执行（跳过函数）")

            await self.execute_command("next")
            #await self._update_debug_state()

            return True

        except Exception as e:
            logger.error(f"单步执行失败: {e}")
            return False
    
    async def step_into(self) -> bool:
        """单步执行（进入函数调用）"""
        try:
            logger.info("单步执行（进入函数）")

            await self.execute_command("step")
            #await self._update_debug_state()

            return True

        except Exception as e:
            logger.error(f"单步执行失败: {e}")
            return False
    
    async def step_out(self) -> bool:
        """跳出当前函数"""
        try:
            logger.info("跳出当前函数")

            await self.execute_command("finish")
            #await self._update_debug_state()

            return True

        except Exception as e:
            logger.error(f"跳出函数失败: {e}")
            return False
    
    async def get_backtrace(self) -> List[StackFrame]:
        """获取调用栈"""
        try:
            output = await self.execute_command("backtrace")
            return self._parse_backtrace(output)
            
        except BaseException as e:
            logger.error(f"获取调用栈失败: {e}")
            return []
    
    async def get_variables(self, scope: str = "local") -> Dict[str, Variable]:
        """获取变量信息"""
        try:
            if scope == "local":
                output = await self.execute_command("info locals")
            elif scope == "args":
                output = await self.execute_command("info args")
            else:
                output = await self.execute_command(f"info variables {scope}")
            
            return self._parse_variables(output)
            
        except Exception as e:
            logger.error(f"获取变量信息失败: {e}")
            return {}
    
    async def evaluate_expression(self, expression: str) -> str:
        """计算表达式"""
        try:
            output = await self.execute_command(f"print {expression}")
            return self._parse_print_output(output)
            
        except Exception as e:
            logger.error(f"计算表达式失败: {e}")
            return ""
    
    async def get_memory_info(self, address: str, size: int = 16) -> str:
        """获取内存信息"""
        try:
            output = await self.execute_command(f"x/{size}x {address}")
            return output
            
        except Exception as e:
            logger.error(f"获取内存信息失败: {e}")
            return ""
    
    async def _update_debug_state(self) -> DebugState:
        """更新调试状态"""
        try:
            # 获取当前状态
            status_output = await self.execute_command("info program")
            
            # 解析程序状态
            if "not being run" in status_output.lower():
                self.debug_state.is_running = False
                self.debug_state.is_stopped = False
            elif "stopped" in status_output.lower():
                self.debug_state.is_running = False
                self.debug_state.is_stopped = True
                # 检查是否是断点停止的
                if "stopped at breakpoint" in status_output.lower():
                    self.debug_state.stop_reason = "breakpoint"
                else:
                    self.debug_state.stop_reason = "SIGSEGV"
            else:
                self.debug_state.is_running = True
                self.debug_state.is_stopped = False
            
            # 如果程序停止，获取详细信息
            if self.debug_state.is_stopped:
                # 获取调用栈
                self.debug_state.stack_frames = await self.get_backtrace()
                if self.debug_state.stack_frames:
                    self.debug_state.current_frame = self.debug_state.stack_frames[0]
                
                # 获取局部变量
                self.debug_state.variables = await self.get_variables("local")
                
                # 获取断点信息
                await self._get_breakpoints_info()
        except BaseException as e:
            logger.error(f"更新调试状态失败: {e}")
        
        return self.debug_state
    
    async def _get_breakpoints_info(self):
        """更新断点信息"""
        try:
            output = await self.execute_command("info breakpoints")
            self.debug_state.breakpoints = self._parse_breakpoints(output)
            
        except Exception as e:
            logger.error(f"更新断点信息失败: {e}")
    
    def _parse_backtrace(self, output: str) -> List[StackFrame]:
        """解析调用栈输出，支持多种格式"""
        frames = []

        try:
            # 支持多种 backtrace 输出格式
            patterns = [
                # GDB MI 模式输出格式1: ~"#0  function (args) at file:line\n"
                re.compile(r'~"#(\d+)\s+(.+?)\s+\(([^)]*)\)\s+at\s+(.+?):(\d+)\\n"'),

                # GDB MI 模式输出格式2: ~"#0  0x123456 in function (args) at file:line\n"
                re.compile(r'~"#(\d+)\s+0x[0-9a-fA-F]+\s+in\s+(.+?)\s+\(([^)]*)\)\s+at\s+(.+?):(\d+)\\n"'),

                # 标准格式: #0  0x00007ffff7a05b97 in __GI_raise (sig=6) at ../sysdeps/unix/sysv/linux/raise.c:51
                re.compile(r'#(\d+)\s+0x[0-9a-fA-F]+\s+in\s+(.+?)\s+\(([^)]*)\)\s+at\s+(.+?):(\d+)'),

                # 简化格式: #0  main () at main.cpp:10
                re.compile(r'#(\d+)\s+(.+?)\s+\(([^)]*)\)\s+at\s+(.+?):(\d+)'),

                # 无参数格式: #0  main at main.cpp:10
                re.compile(r'#(\d+)\s+(.+?)\s+at\s+(.+?):(\d+)'),

                # 地址格式: #0  0x00007ffff7a05b97 in function_name
                re.compile(r'#(\d+)\s+0x[0-9a-fA-F]+\s+in\s+(.+?)(?:\s|$)'),

                # 最简格式: #0  function_name
                re.compile(r'#(\d+)\s+(.+?)(?:\s|$)'),

                # 地址停止格式
                re.compile(r'Program stopped at (0x[0-9a-fA-F]+)\.$'),

                # 断点停止格式
                re.compile(r'It stopped at breakpoint (\d+)\.$')
            ]

            # 预处理输出，提取 MI 模式中的实际内容
            processed_lines = []
            for line in output.split('\n'):
                line = line.strip()
                if not line:
                    continue

                # 如果是 MI 模式的输出，直接添加
                if line.startswith('~"#') and '\\n"' in line:
                    processed_lines.append(line)
                # 如果是普通格式的栈帧，也添加
                elif line.startswith('#'):
                    processed_lines.append(line)
                # 其他特殊格式
                elif 'Program stopped at' in line or 'It stopped at breakpoint' in line:
                    processed_lines.append(line)

            for line in processed_lines:
                frame_parsed = False
                for pattern in patterns:
                    match = pattern.search(line)
                    if match:
                        try:
                            if 'Program stopped at' in pattern.pattern:
                                # 匹配地址停止
                                address = match.group(1)
                                frame = StackFrame(
                                    level=0,
                                    function="",
                                    file="",
                                    line=0,
                                    address=address,
                                    args={}
                                )
                                frames.append(frame)
                                frame_parsed = True
                                break
                            elif 'It stopped at breakpoint' in pattern.pattern:
                                # 记录断点编号
                                self.debug_state.stop_reason = f"breakpoint {match.group(1)}"
                                frame_parsed = True
                                break
                            else:
                                # 解析栈帧信息
                                level = int(match.group(1))
                                raw_function = match.group(2).strip()

                                # 提取地址信息（如果存在）
                                address = ""
                                function = raw_function

                                # 检查是否包含地址信息 (0x123456 in function_name)
                                addr_match = re.match(r'0x[0-9a-fA-F]+\s+in\s+(.+)', raw_function)
                                if addr_match:
                                    address = raw_function.split()[0]  # 提取地址
                                    function = addr_match.group(1)    # 提取函数名

                                # 根据匹配组数量确定参数、文件和行号的位置
                                if len(match.groups()) >= 5:
                                    # 有参数的格式
                                    args = match.group(3).strip() if match.group(3) else ""
                                    file = match.group(4).strip()
                                    line_num = int(match.group(5))
                                elif len(match.groups()) >= 4:
                                    # 无参数格式
                                    args = ""
                                    file = match.group(3).strip()
                                    line_num = int(match.group(4))
                                else:
                                    # 只有函数名
                                    args = ""
                                    file = ""
                                    line_num = 0

                                # 对于 MI 模式，需要特殊处理函数名中包含参数的情况
                                if '~"#' in line and '(' in function and ')' in function:
                                    # 尝试分离函数名和参数
                                    paren_pos = function.find('(')
                                    if paren_pos > 0:
                                        func_name = function[:paren_pos].strip()
                                        # 如果没有从正则中获取到参数，从函数字符串中提取
                                        if not args:
                                            args_start = function.find('(')
                                            args_end = function.rfind(')')
                                            if args_start >= 0 and args_end > args_start:
                                                args = function[args_start+1:args_end]
                                        function = func_name

                                frame = StackFrame(
                                    level=level,
                                    function=function,
                                    file=file,
                                    line=line_num,
                                    address=address,
                                    args={"raw": args} if args else {}
                                )
                                frames.append(frame)
                                frame_parsed = True
                                break

                        except (ValueError, IndexError) as e:
                            logger.debug(f"解析栈帧失败: {line}, 错误: {e}")
                            continue

                if not frame_parsed:
                    logger.debug(f"无法解析栈帧行: {line}")

        except Exception as e:
            logger.error(f"解析backtrace失败: {e}")

        logger.debug(f"解析到 {len(frames)} 个栈帧")
        return frames
    
    def _parse_variables(self, output: str) -> Dict[str, Variable]:
        """解析变量输出"""
        variables = {}
        
        # 简单的变量解析
        for line in output.split('\n'):
            if '=' in line:
                parts = line.split('=', 1)
                if len(parts) == 2:
                    name = parts[0].strip()
                    value = parts[1].strip()
                    
                    variable = Variable(
                        name=name,
                        type="unknown",
                        value=value
                    )
                    variables[name] = variable
        
        return variables
    
    def _parse_print_output(self, output: str) -> str:
        """解析print命令输出"""
        lines = output.split('\n')
        for line in lines:
            if line.startswith('$'):
                # 找到结果行
                parts = line.split('=', 1)
                if len(parts) == 2:
                    return parts[1].strip()
        
        return output
    
    def _parse_breakpoints(self, output: str) -> List[BreakpointInfo]:
        """解析断点信息，支持多种格式"""
        breakpoints = []

        try:
            # 预处理输出，提取 MI 模式中的实际内容
            processed_lines = []
            current_breakpoint_lines = []

            for line in output.split('\n'):
                line = line.strip()
                if not line:
                    continue

                # 如果是 MI 模式的输出，提取内容
                if line.startswith('~"') and line.endswith('\\n"'):
                    # 提取引号内的内容，去掉转义
                    content = line[2:-3]  # 去掉 ~" 和 \n"
                    content = content.replace('\\t', '\t')  # 处理制表符
                    processed_lines.append(content)
                # 如果是普通格式的断点信息，也添加
                elif not line.startswith('&') and not line.startswith('^') and not line.startswith('(gdb)'):
                    processed_lines.append(line)

            # 解析断点信息
            i = 0
            while i < len(processed_lines):
                line = processed_lines[i].strip()

                # 跳过标题行
                if line.startswith('Num') and 'Type' in line:
                    i += 1
                    continue

                # 解析断点行
                if line and not line.startswith('\t'):
                    # 使用正则表达式解析断点信息
                    # 格式: 1       breakpoint     keep y   0x0000555555561b17 in function at file:line
                    import re
                    bp_pattern = re.compile(r'^(\d+)\s+(\w+)\s+(\w+)\s+([yn])\s+(0x[0-9a-fA-F]+)?\s*(.*)$')
                    match = bp_pattern.match(line)

                    if match:
                        try:
                            number = int(match.group(1))
                            type_info = match.group(2)
                            disposition = match.group(3)
                            enabled = match.group(4) == 'y'
                            address = match.group(5) if match.group(5) else ""
                            location_info = match.group(6).strip()

                            # 收集额外信息（如命中次数）
                            hit_count = 0
                            j = i + 1
                            while j < len(processed_lines) and processed_lines[j].startswith('\t'):
                                extra_line = processed_lines[j].strip()
                                # 解析命中次数
                                if 'already hit' in extra_line:
                                    hit_match = re.search(r'hit (\d+) time', extra_line)
                                    if hit_match:
                                        hit_count = int(hit_match.group(1))
                                j += 1

                            bp = BreakpointInfo(
                                number=number,
                                type=type_info,
                                disposition=disposition,
                                enabled=enabled,
                                address=address,
                                location=location_info,
                                hit_count=hit_count
                            )
                            breakpoints.append(bp)

                            # 跳过已处理的额外行
                            i = j - 1

                        except (ValueError, IndexError) as e:
                            logger.debug(f"解析断点行失败: {line}, 错误: {e}")

                i += 1

        except Exception as e:
            logger.error(f"解析断点信息失败: {e}")

        logger.debug(f"解析到 {len(breakpoints)} 个断点")
        return breakpoints
    
    async def close_session(self):
        """关闭GDB会话"""
        try:
            if self.process:
                logger.info("关闭GDB会话")

                # 计算会话持续时间
                session_duration = 0
                if self._session_start_time:
                    session_duration = time.time() - self._session_start_time

                # 尝试优雅退出
                exit_code = None
                try:
                    await self.execute_command("quit")
                    await asyncio.sleep(1)
                    exit_code = self.process.poll()
                except:
                    pass

                # 强制终止进程
                if self.process.poll() is None:
                    self.process.terminate()
                    await asyncio.sleep(1)

                    if self.process.poll() is None:
                        self.process.kill()
                        exit_code = -9  # SIGKILL
                    else:
                        exit_code = self.process.poll()

                # 记录会话结束
                self.debug_logger.log_gdb_session_end(session_duration, exit_code)

                self.process = None
                self.is_connected = False

                logger.info("GDB会话已关闭")

        except BaseException as e:
            logger.error(f"关闭GDB会话失败: {e}")
    
    async def get_debug_state(self) -> DebugState:
        """获取当前调试状态"""
        debug_state = await self._update_debug_state()
        return debug_state
